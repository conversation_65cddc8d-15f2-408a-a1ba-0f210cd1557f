{"name": "dynamic-build", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.2.8", "@tanstack/react-query": "^5.87.1", "@types/node": "^18.15.0", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/styled-components": "^5.1.34", "@vanilla-extract/css": "^1.17.4", "@vanilla-extract/vite-plugin": "^5.1.1", "axios": "^1.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.18", "dplayer": "^1.27.1", "hls.js": "^1.6.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.30.1", "styled-components": "^6.1.19", "swiper": "^12.0.2", "typescript": "^4.9.5"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.87.1", "@types/crypto-js": "^4.2.2", "@types/dplayer": "^1.25.5", "@types/hls.js": "^0.13.3", "@vitejs/plugin-react": "^4.3.0", "autoprefixer": "^10.4.14", "css-loader": "^6.7.3", "postcss": "^8.4.21", "style-loader": "^3.3.2", "tailwindcss": "^3.2.7", "vite": "^5.4.0"}, "scripts": {"dev": "node scripts/dev.js", "build": "node scripts/build.js", "build:template": "node scripts/build-template.js", "preview": "node scripts/preview.js"}}
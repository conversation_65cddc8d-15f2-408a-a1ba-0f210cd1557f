/* Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles for the dynamic build system */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  min-height: 100vh;
}

/* Reset some default styles */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
}

p {
  margin: 0;
}

button {
  font-family: inherit;
}

/* Utility classes for the demo */
.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  background-color: #f9fafb;
}

/* Custom Swiper Pagination Styles */
.banner-swiper .swiper-pagination {
  position: relative;
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.banner-swiper .swiper-pagination-bullet {
  width: 2rem;
  height: 0.5rem;
  border-radius: 0.25rem;
  background-color: #ffffff;
  opacity: 1;
  transition: all 0.3s ease;
  margin: 0 !important;
}

.banner-swiper .swiper-pagination-bullet-active {
  background-color: #F86294;
  transform: scale(1.1);
}

/* Category Grid Swiper Pagination Styles */
.category-swiper .swiper-pagination {
  position: relative;
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.25rem;
}

.category-swiper .custom-pagination-dot {
  width: 1.5rem;
  height: 0.5rem;
  border-radius: 0.25rem;
  background-color: #d1d5db;
  opacity: 1;
  transition: all 0.3s ease;
  margin: 0 !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.category-swiper .custom-pagination-dot-active {
  background: linear-gradient(90deg, #F86294 0%, #F86294 70%, #d1d5db 70%, #d1d5db 100%);
  transform: scale(1.05);
}

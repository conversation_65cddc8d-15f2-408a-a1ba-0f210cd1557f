import React, { useState } from "react";
import { HomepageProps } from "../../types/index";
import {
  useAdsData,
  useHomeData,
  useWalkThroughData,
} from "../../hooks/useApi";
import VideoCard from "../../components/VideoCard";
import Image from "../../components/Image";
import Tabs from "../../components/Tabs";
import CategoryGrid from "../../components/CategoryGrid";
import { SECTION_NAMES } from "../../constants/sections";

const Homepage: React.FC<HomepageProps> = () => {
  const { data: apiData, isLoading, error } = useHomeData();
  const { data: adsData } = useAdsData();
  const { data: walkThroughData } = useWalkThroughData();
  const [activeTab, setActiveTab] = useState("官推");

  // Tab data with more categories for expandable functionality
  const tabs = [
    { name: "官推", count: 6 },
    { name: "异色", count: 0 },
    { name: "影线", count: 3 },
    { name: "小说", count: 0 },
    { name: "特区", count: 0 },
    { name: "在线电影", count: 5 },
    { name: "有声小说", count: 2 },
    { name: "热销专题", count: 0 },
    { name: "限时免费", count: 1 },
    { name: "VIP专区", count: 0 },
    { name: "另类色情", count: 0 },
    { name: "请选择分类", count: 0 },
  ];

  const handleTabChange = (tabName: string) => {
    setActiveTab(tabName);
    // Handle tab change logic here
  };

  // Category grid data with placeholder images
  const categories = [
    {
      id: "1",
      title: "反差 少女",
      image: "https://via.placeholder.com/300x300/FFE4B5/000000?text=反差+少女",
      backgroundColor: "#FFE4B5",
    },
    {
      id: "2",
      title: "裸播 大赛",
      image: "https://via.placeholder.com/300x300/FFB6C1/000000?text=裸播+大赛",
      backgroundColor: "#FFB6C1",
    },
    {
      id: "3",
      title: "主播 艳舞",
      image: "https://via.placeholder.com/300x300/D3D3D3/000000?text=主播+艳舞",
      backgroundColor: "#D3D3D3",
    },
    {
      id: "4",
      title: "私人 订制",
      image: "https://via.placeholder.com/300x300/F5DEB3/000000?text=私人+订制",
      backgroundColor: "#F5DEB3",
    },
    {
      id: "5",
      title: "吃瓜 黑料",
      image: "https://via.placeholder.com/300x300/AFEEEE/000000?text=吃瓜+黑料",
      backgroundColor: "#AFEEEE",
    },
    {
      id: "6",
      title: "绳艺 调教",
      image: "https://via.placeholder.com/300x300/ADD8E6/000000?text=绳艺+调教",
      backgroundColor: "#ADD8E6",
    },
    {
      id: "7",
      title: "觅圈 正妹",
      image: "https://via.placeholder.com/300x300/E0F6FF/000000?text=觅圈+正妹",
      backgroundColor: "#E0F6FF",
    },
    {
      id: "8",
      title: "黑丝 玉足",
      image: "https://via.placeholder.com/300x300/DDA0DD/000000?text=黑丝+玉足",
      backgroundColor: "#DDA0DD",
    },
    {
      id: "9",
      title: "制服 诱惑",
      image: "https://via.placeholder.com/300x300/FFE4E1/000000?text=制服+诱惑",
      backgroundColor: "#FFE4E1",
    },
    {
      id: "10",
      title: "清纯 学生",
      image: "https://via.placeholder.com/300x300/F0F8FF/000000?text=清纯+学生",
      backgroundColor: "#F0F8FF",
    },
    {
      id: "11",
      title: "熟女 风情",
      image: "https://via.placeholder.com/300x300/FFF8DC/000000?text=熟女+风情",
      backgroundColor: "#FFF8DC",
    },
    {
      id: "12",
      title: "cosplay 角色",
      image:
        "https://via.placeholder.com/300x300/E6E6FA/000000?text=cosplay+角色",
      backgroundColor: "#E6E6FA",
    },
  ];
  if (isLoading) {
    return (
      <div className="px-4 py-6">
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mb-4"></div>
          <p className="text-gray-500">Loading content...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="px-4 py-6">
        <div className="text-center">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <p className="text-red-600">
              Error loading content: {error.message}
            </p>
            <p className="text-gray-500 mt-2">Showing fallback content...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Tabs Section */}
      <Tabs
        tabs={tabs}
        initialActiveTab={activeTab}
        onTabChange={handleTabChange}
        maxVisibleTabs={5}
      />

      {/* Category Grid Section */}
      <CategoryGrid categories={categories} itemsPerPage={8} />

      <div className="px-4 py-6">
        {/* Ads Section */}
        {adsData?.data && (
          <div className="mb-16">
            {/* Horizontal Ads */}
            {adsData.data.ads?.shouyehengfu &&
              adsData.data.ads.shouyehengfu.length > 0 && (
                <div className="mb-8">
                  <div className="grid grid-cols-1 gap-4">
                    {adsData.data.ads.shouyehengfu
                      .filter((ad) => ad.status === 1) // Only show active ads
                      .map((ad, index) => (
                        <a
                          key={index}
                          href={ad.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block group"
                        >
                          <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300">
                            <Image
                              src={ad.local_img || ad.img}
                              alt={ad.name}
                              className="w-full h-16 object-cover group-hover:scale-105 transition-transform duration-300"
                              width="100%"
                              height="4rem"
                            />

                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-2">
                              <h4 className="text-white font-medium text-xs">
                                {ad.name}
                              </h4>
                            </div>
                          </div>
                        </a>
                      ))}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* API Content Section */}
        {apiData?.data && (
          <div className="space-y-16">
            {Object.entries(apiData.data)
              .filter(([key]) => key !== "seo" && key !== "notice")
              .map(([, value], i) => {
                if (!value || !value.data || value.data.length === 0) {
                  return null;
                }

                const title =
                  value.channel !== "topic"
                    ? SECTION_NAMES[i]
                    : value.data[0]?.title;
                const listData =
                  value.channel === "topic" ? value.data[0]?.list : value.data;

                return (
                  <div key={i} className="mb-8">
                    <h3
                      className="text-lg font-semibold text-gray-900 mb-3"
                      style={{
                        fontSize: "1.125rem",
                        fontWeight: "600",
                        color: "#111827",
                        marginBottom: "0.75rem",
                      }}
                    >
                      {title}
                    </h3>
                    <div className="grid grid-cols-2 gap-3">
                      {listData.map((item: any) => (
                        <VideoCard
                          key={item.id}
                          id={item.id}
                          title={item.title}
                          performer={item.publisher || ""}
                          thumb={item.thumb}
                          duration={item.duration}
                          insert_time={item.insert_time}
                          isVip={item.channel === "vip"}
                        />
                      ))}
                    </div>
                  </div>
                );
              })}
          </div>
        )}
      </div>
    </div>
  );
};

export default Homepage;

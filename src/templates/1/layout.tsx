import React from "react";
import { Link, useLocation } from "react-router-dom";
import { LayoutProps } from "../../types/index";
import { HEADER_MENU } from "../../constants/menu";
import { SITE_SETTINGS } from "../../constants/site";
import Banner from "../../components/Banner";

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isHomepage = location.pathname === "/";

  return (
    <div className="min-h-screen bg-gray-50 font-serif flex flex-col">
      {/* Mobile-first container with fixed width on desktop */}
      <div className="w-full max-w-md mx-auto md:max-w-md lg:max-w-md xl:max-w-md 2xl:max-w-md">
        <header
          className={`shadow-sm ${isHomepage ? "rounded-b-2xl" : ""}`}
          style={{
            background:
              "linear-gradient(155.37deg, #F4A5AB 21.32%, #F5C8CE 91.28%)",
          }}
        >
          <div className="px-4">
            <div className="flex justify-between items-center py-4">
              <div>
                <Link to="/" className="block">
                  <img
                    src="/logo.png"
                    alt={SITE_SETTINGS.title}
                    className="h-8 w-auto cursor-pointer hover:opacity-80 transition-opacity"
                  />
                </Link>
              </div>
              <nav className="flex space-x-4">
                {HEADER_MENU.slice(0, 3).map((item, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      // Handle navigation based on channel or code
                    }}
                    className="text-white hover:text-gray-200 transition-colors duration-300 bg-transparent border-none cursor-pointer text-sm"
                  >
                    {item.name}
                  </button>
                ))}
              </nav>
            </div>
          </div>
          {/* Banner Section - Only show on homepage */}
          {isHomepage && <Banner />}
        </header>

        <main className="flex-1">{children}</main>

        <footer className="bg-gray-800 text-white py-6">
          <div className="px-4 text-center">
            <p className="text-gray-300 m-0 text-sm">
              © 2024 {SITE_SETTINGS.title}. 高质量视频内容平台.
            </p>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default Layout;

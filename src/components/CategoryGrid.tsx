import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";
import "swiper/css";
import "swiper/css/pagination";
import Image from "./Image";

interface CategoryItem {
  id: string;
  title: string;
  image: string;
  backgroundColor: string;
}

interface CategoryGridProps {
  categories: CategoryItem[];
  itemsPerPage?: number;
  className?: string;
}

const CategoryGrid: React.FC<CategoryGridProps> = ({
  categories,
  itemsPerPage = 8,
  className = "",
}) => {
  // Split categories into pages
  const pages = [];
  for (let i = 0; i < categories.length; i += itemsPerPage) {
    pages.push(categories.slice(i, i + itemsPerPage));
  }

  if (categories.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white ${className}`}>
      <Swiper
        modules={[Pagination]}
        spaceBetween={0}
        slidesPerView={1}
        pagination={{
          clickable: true,
          renderBullet: (_index: number, className: string) => {
            return `<span class="${className} custom-pagination-dot"></span>`;
          },
        }}
        className="category-swiper"
      >
        {pages.map((pageCategories, pageIndex) => (
          <SwiperSlide key={pageIndex}>
            <div className="px-4 py-6">
              <div className="grid grid-cols-4 gap-3">
                {pageCategories.map((category) => (
                  <div
                    key={category.id}
                    className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 cursor-pointer group"
                    style={{ backgroundColor: category.backgroundColor }}
                  >
                    <div className="aspect-square relative">
                      <Image
                        src={category.image}
                        alt={category.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                        width="100%"
                        height="100%"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 p-2">
                      <h3 className="text-white font-bold text-xs text-center leading-tight">
                        {category.title}
                      </h3>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default CategoryGrid;

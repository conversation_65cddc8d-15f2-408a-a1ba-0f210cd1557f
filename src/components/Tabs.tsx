import React, { useState, useRef, useEffect } from "react";
import { Tab } from "@headlessui/react";

interface TabItem {
  name: string;
  count?: number;
  icon?: string;
}

interface TabsProps {
  tabs: TabItem[];
  initialActiveTab?: string;
  onTabChange?: (tabName: string) => void;
  className?: string;
  maxVisibleTabs?: number;
}

const Tabs: React.FC<TabsProps> = ({
  tabs,
  onTabChange,
  className = "",
  maxVisibleTabs = 5,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const underlineRef = useRef<HTMLDivElement>(null);
  const tabRefs = useRef<(HTMLElement | null)[]>([]);

  // Get visible tabs (max 5) + 全部 button
  const visibleTabs = tabs.slice(0, maxVisibleTabs);
  const allTabs = [...visibleTabs, { name: "全部", count: 0 }];

  // Update underline position when selectedIndex changes
  useEffect(() => {
    if (underlineRef.current && tabRefs.current[selectedIndex]) {
      const activeTab = tabRefs.current[selectedIndex];
      if (activeTab) {
        const tabRect = activeTab.getBoundingClientRect();
        const containerRect = activeTab.parentElement?.getBoundingClientRect();
        if (containerRect) {
          const left = tabRect.left - containerRect.left;
          const width = tabRect.width;
          underlineRef.current.style.left = `${left}px`;
          underlineRef.current.style.width = `${width}px`;
        }
      }
    }
  }, [selectedIndex, isExpanded]);

  const handleTabChange = (index: number) => {
    const tab = allTabs[index];
    if (tab.name === "全部") {
      setIsExpanded(!isExpanded);
    } else {
      setSelectedIndex(index);
      setIsExpanded(false);
      if (onTabChange) {
        onTabChange(tab.name);
      }
    }
  };

  return (
    <div className={`bg-white border-b border-gray-200 ${className}`}>
      <Tab.Group selectedIndex={selectedIndex} onChange={handleTabChange}>
        <Tab.List className="flex justify-between items-center px-4 py-3">
          {allTabs.map((tab, index) => (
            <Tab
              key={index}
              ref={(el) => (tabRefs.current[index] = el)}
              className={({ selected }) =>
                `relative flex items-center justify-center px-2 py-1 text-sm font-medium transition-colors duration-300 focus:outline-none ${
                  selected
                    ? "text-[#F86294]"
                    : "text-gray-700 hover:text-gray-900"
                }`
              }
            >
              {/* Badge */}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="absolute -top-1 -right-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                  {tab.count}
                </span>
              )}

              {/* Tab Content */}
              <div className="flex items-center">
                {tab.icon ? (
                  <span className="text-lg">{tab.icon}</span>
                ) : (
                  <span>{tab.name}</span>
                )}
                {/* Arrow icon for 全部 tab */}
                {tab.name === "全部" && (
                  <span className="ml-1 text-xs">{isExpanded ? "▲" : "▼"}</span>
                )}
              </div>
            </Tab>
          ))}
        </Tab.List>

        {/* Underline indicator */}
        <div className="relative">
          <div
            ref={underlineRef}
            className="absolute bottom-0 h-0.5 transition-all duration-300"
            style={{ left: 0, width: 0, backgroundColor: "#F86294" }}
          />
        </div>
      </Tab.Group>

      {/* Expanded Categories (when 全部 is clicked) */}
      {isExpanded && (
        <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-2">
            {tabs.slice(maxVisibleTabs).map((tab, index) => (
              <button
                key={`expanded-${index}`}
                className={`flex items-center justify-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-300 ${
                  selectedIndex === maxVisibleTabs + index
                    ? "text-white"
                    : "bg-white text-gray-700 hover:bg-gray-100"
                }`}
                style={{
                  backgroundColor:
                    selectedIndex === maxVisibleTabs + index
                      ? "#F86294"
                      : undefined,
                }}
                onClick={() => {
                  setSelectedIndex(maxVisibleTabs + index);
                  setIsExpanded(false);
                  if (onTabChange) {
                    onTabChange(tab.name);
                  }
                }}
              >
                {tab.count !== undefined && tab.count > 0 && (
                  <span className="ml-1 inline-flex items-center justify-center w-4 h-4 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                    {tab.count}
                  </span>
                )}
                <span>{tab.name}</span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Tabs;

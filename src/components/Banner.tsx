import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import Image from "./Image";
import { useAdsData } from "../hooks/useApi";

interface BannerProps {
  className?: string;
  showPagination?: boolean;
  showNavigation?: boolean;
  autoplay?: boolean;
  autoplayDelay?: number;
  height?: string;
  rounded?: boolean;
}

const Banner: React.FC<BannerProps> = ({
  className = "",
  showPagination = true,
  showNavigation = true,
  autoplay = true,
  autoplayDelay = 5000,
  height = "h-48",
  rounded = true,
}) => {
  const { data: adsData } = useAdsData();
  const banners = adsData?.data?.banners || [];

  if (!banners || banners.length === 0) {
    return null;
  }

  return (
    <div className={`w-full px-4 py-4 ${className}`}>
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={showNavigation}
        pagination={showPagination ? { clickable: true } : false}
        autoplay={
          autoplay
            ? {
                delay: autoplayDelay,
                disableOnInteraction: false,
              }
            : false
        }
        loop={banners.length > 1}
        className="banner-swiper"
      >
        {banners.map((banner, index) => (
          <SwiperSlide key={index}>
            <a
              href={banner.url}
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <div
                className={`relative overflow-hidden shadow-lg ${
                  rounded ? "rounded-lg" : ""
                }`}
              >
                <Image
                  src={banner.local_img || banner.img}
                  alt={banner.title}
                  className={`w-full ${height} object-cover`}
                  width="100%"
                  height="12rem"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-70"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <h4 className="text-white font-bold text-lg leading-tight">
                    {banner.title}
                  </h4>
                </div>
              </div>
            </a>
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default Banner;
